import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Upload, List, Modal, Form, message, Tabs, Spin, Radio, Tooltip } from 'antd';
import { PlusOutlined, UploadOutlined, DeleteOutlined, FileTextOutlined, InfoCircleOutlined } from '@ant-design/icons';
import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

interface KnowledgeBaseManagerProps {
  apiKey: string;
  apiUrl: string;
}

interface Dataset {
  id: string;
  name: string;
  description?: string;
  created_at: number;
}

interface Document {
  id: string;
  name: string;
  created_at: number;
  indexing_status: string;
  display_status?: string;
  word_count?: number;
  enabled?: boolean;
}

const KnowledgeBaseManager: React.FC<KnowledgeBaseManagerProps> = ({ apiKey }) => {
  const [datasets, setDatasets] = useState<Dataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [selectedDataset, setSelectedDataset] = useState<Dataset | null>(null);
  const [documents, setDocuments] = useState<Document[]>([]);
  const [documentModalVisible, setDocumentModalVisible] = useState(false);
  const [textModalVisible, setTextModalVisible] = useState(false);
  const [indexingTechnique, setIndexingTechnique] = useState<'high_quality' | 'economy'>('high_quality');
  const [form] = Form.useForm();
  const [textForm] = Form.useForm();
  
  // Radio.Button圆角样式对象
  const radioButtonStyle = {
    borderRadius: '8px',
    width: '100%'
  };

  // 加载知识库列表
  const fetchDatasets = async () => {
    try {
      setLoading(true);
      // 临时硬编码测试 - 确认 API 地址正确
      const apiUrl = import.meta.env.DEV
        ? 'http://127.0.0.1:3001/api/knowledge-base/datasets'
        : 'https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets';
      console.log('请求 API 地址:', apiUrl);
      const response = await axios.get(apiUrl);
      setDatasets(response.data.data || []);
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载文档列表
  const fetchDocuments = async (datasetId: string) => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents`);
      setDocuments(response.data.data || []);
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建知识库
  const createDataset = async (values: any) => {
    try {
      setLoading(true);
      await axios.post(`${API_BASE_URL}/knowledge-base/datasets`, {
        name: values.name,
        description: values.description || ''
      });
      message.success('创建知识库成功');
      setCreateModalVisible(false);
      form.resetFields();
      fetchDatasets();
    } catch (error) {
      console.error('创建知识库失败:', error);
      message.error('创建知识库失败');
    } finally {
      setLoading(false);
    }
  };

  // 通过文本创建文档
  const createDocumentByText = async (values: any) => {
    if (!selectedDataset) return;

    try {
      setLoading(true);
      await axios.post(`${API_BASE_URL}/knowledge-base/datasets/${selectedDataset.id}/document/create_by_text`, {
        name: values.name,
        text: values.content,
        indexing_technique: indexingTechnique,
        process_rule: { mode: 'automatic' }
      });
      message.success('创建文档成功');
      setTextModalVisible(false);
      textForm.resetFields();
      fetchDocuments(selectedDataset.id);
    } catch (error) {
      console.error('创建文档失败:', error);
      message.error('创建文档失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除文档
  const deleteDocument = async (documentId: string) => {
    if (!selectedDataset) return;

    try {
      setLoading(true);
      await axios.delete(`${API_BASE_URL}/knowledge-base/datasets/${selectedDataset.id}/documents/${documentId}`);
      message.success('删除文档成功');
      fetchDocuments(selectedDataset.id);
    } catch (error) {
      console.error('删除文档失败:', error);
      message.error('删除文档失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取知识库列表
  useEffect(() => {
    fetchDatasets();
  }, []);

  // 选择知识库时加载文档
  useEffect(() => {
    if (selectedDataset) {
      fetchDocuments(selectedDataset.id);
    }
  }, [selectedDataset]);

  return (
    <Card 
      title="知识库管理" 
      style={{ 
        marginBottom: '24px',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
        borderRadius: '8px'
      }}
    >
      <Tabs
        type="card"
        activeKey={selectedDataset ? 'documents' : 'datasets'}
        items={[
          {
            key: 'datasets',
            label: '知识库列表',
            children: (
              <>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  {datasets.length === 0 && (
                    <Button 
                      type="primary" 
                      icon={<PlusOutlined />} 
                      onClick={() => setCreateModalVisible(true)}
                    >
                      创建知识库
                    </Button>
                  )}
                </div>
                
                {loading ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin />
                  </div>
                ) : (
                  <List
                    dataSource={datasets}
                    renderItem={(item) => (
                      <List.Item
                        actions={[
                          <Button 
                            key="select" 
                            type="link" 
                            onClick={() => setSelectedDataset(item)}
                          >
                            管理文档
                          </Button>,
                          // <Button 
                          //   key="delete" 
                          //   type="link" 
                          //   danger 
                          //   onClick={() => {
                          //     Modal.confirm({
                          //       title: '确认删除',
                          //       content: `确定要删除知识库 "${item.name}" 吗？此操作不可恢复。`,
                          //       onOk: () => deleteDataset(item.id)
                          //     });
                          //   }}
                          // >
                          //   删除
                          // </Button>
                        ]}
                      >
                        <List.Item.Meta
                          title={item.name}
                          description={item.description || '无描述'}
                        />
                        <div>创建时间: {new Date(item.created_at * 1000).toLocaleString()}</div>
                      </List.Item>
                    )}
                  />
                )}
              </>
            )
          },
          {
            key: 'documents',
            label: selectedDataset ? `${selectedDataset.name} 的文档` : '文档管理',
            disabled: !selectedDataset,
            children: selectedDataset && (
              <>
                <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <Button 
                      onClick={() => setSelectedDataset(null)} 
                      style={{ marginRight: '8px' }}
                    >
                      返回知识库列表
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<FileTextOutlined />} 
                      onClick={() => setTextModalVisible(true)}
                      style={{ marginRight: '8px' }}
                    >
                      添加文本
                    </Button>
                    <Button 
                      type="primary" 
                      icon={<UploadOutlined />} 
                      onClick={() => setDocumentModalVisible(true)}
                    >
                      上传文件
                    </Button>
                  </div>
                </div>
                
                {loading ? (
                  <div style={{ textAlign: 'center', padding: '20px' }}>
                    <Spin />
                  </div>
                ) : (
                  <List
                    dataSource={documents}
                    renderItem={(item) => (
                      <List.Item
                        actions={[
                          <Button 
                            key="delete" 
                            type="link" 
                            danger 
                            icon={<DeleteOutlined />}
                            onClick={() => {
                              Modal.confirm({
                                title: '确认删除',
                                content: `确定要删除文档 "${item.name}" 吗？此操作不可恢复。`,
                                onOk: () => deleteDocument(item.id)
                              });
                            }}
                          >
                            删除
                          </Button>
                        ]}
                      >
                        <List.Item.Meta
                          title={item.name}
                          description={`状态: ${item.indexing_status === 'completed' ? '已完成' : '处理中'}`}
                        />
                        <div>创建时间: {new Date(item.created_at * 1000).toLocaleString()}</div>
                      </List.Item>
                    )}
                  />
                )}
              </>
            )
          }
        ]}
      />

      {/* 创建知识库弹窗 */}
      <Modal
        title="创建知识库"
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={createDataset}
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述（可选）"
          >
            <Input.TextArea placeholder="请输入知识库描述" />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加文本文档弹窗 */}
      <Modal
        title="添加文本文档"
        open={textModalVisible}
        onCancel={() => setTextModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={textForm}
          layout="vertical"
          onFinish={createDocumentByText}
        >
          <Form.Item
            name="name"
            label="文档名称"
            rules={[{ required: true, message: '请输入文档名称' }]}
          >
            <Input placeholder="请输入文档名称" />
          </Form.Item>
          <Form.Item
            name="content"
            label="文档内容"
            rules={[{ required: true, message: '请输入文档内容' }]}
          >
            <Input.TextArea 
              placeholder="请输入文档内容" 
              rows={10}
            />
          </Form.Item>
          <Form.Item
            label={
              <span>
                索引技术 
                <Tooltip title="选择不同的索引技术会影响检索质量和token消耗">
                  <InfoCircleOutlined style={{ marginLeft: '4px' }} />
                </Tooltip>
              </span>
            }
          >
            <Radio.Group 
              value={indexingTechnique} 
              onChange={(e) => setIndexingTechnique(e.target.value)}
              style={{ display: 'flex', flexDirection: 'column' }}
            >
              <Radio.Button value="high_quality" style={{ marginBottom: '28px', ...radioButtonStyle }}>
                <div>
                  <div style={{ fontWeight: 'bold' }}>高质量（推荐）</div>
                  <div style={{ fontSize: '12px' }}>调用嵌入模型处理文档以实现更精确的检索，可以帮助LLM生成高质量的答案</div>
                </div>
              </Radio.Button>
              <Radio.Button value="economy" style={radioButtonStyle}>
                <div>
                  <div style={{ fontWeight: 'bold' }}>经济</div>
                  <div style={{ fontSize: '12px' }}>每个数据块使用10个关键词进行检索，不会消耗任何tokens，但会以降低检索准确性为代价</div>
                </div>
              </Radio.Button>
            </Radio.Group>
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              创建
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传文件弹窗 */}
      <Modal
        title="上传文件"
        open={documentModalVisible}
        onCancel={() => setDocumentModalVisible(false)}
        footer={null}
      >
        <div style={{ marginBottom: '16px' }}>
          <div style={{ marginBottom: '8px' }}>
            <span>索引技术</span>
            <Tooltip title="选择不同的索引技术会影响检索质量和token消耗">
              <InfoCircleOutlined style={{ marginLeft: '4px' }} />
            </Tooltip>
          </div>
          <Radio.Group 
            value={indexingTechnique} 
            onChange={(e) => setIndexingTechnique(e.target.value)}
            style={{ marginBottom: '16px', display: 'flex', flexDirection: 'column' }}
          >
            <Radio.Button value="high_quality" style={{ marginBottom: '28px', ...radioButtonStyle }}>
              <div>
                <div style={{ fontWeight: 'bold' }}>高质量（推荐）</div>
                <div style={{ fontSize: '12px' }}>调用嵌入模型处理文档以实现更精确的检索，可以帮助LLM生成高质量的答案</div>
              </div>
            </Radio.Button>
            <Radio.Button value="economy" style={{ marginBottom: '28px', ...radioButtonStyle }}>
              <div>
                <div style={{ fontWeight: 'bold' }}>经济</div>
                <div style={{ fontSize: '12px' }}>每个数据块使用10个关键词进行检索，不会消耗任何tokens，但会降低检索准确性</div>
              </div>
            </Radio.Button>
          </Radio.Group>
        </div>
        <Upload.Dragger
          name="file"
          action={`${API_BASE_URL}/knowledge-base/datasets/${selectedDataset?.id}/document/create_by_file`}
          headers={{
            'Authorization': `Bearer ${apiKey}`
          }}
          accept=".txt,.md,.mdx,.pdf,.html,.xlsx,.xls,.docx,.csv,.vtt,.properties,.htm"
          beforeUpload={(file) => {
            const isLessThan15MB = file.size / 1024 / 1024 < 15;
            if (!isLessThan15MB) {
              message.error('文件大小不能超过 15MB');
            }
            return isLessThan15MB;
          }}
          data={{
            data: JSON.stringify({
              indexing_technique: indexingTechnique,
              process_rule: {
                rules: {
                  pre_processing_rules: [
                    { id: "remove_extra_spaces", enabled: true },
                    { id: "remove_urls_emails", enabled: true }
                  ],
                  segmentation: {
                    separator: "###",
                    max_tokens: 500
                  }
                },
                mode: "custom"
              }
            })
          }}
          onChange={(info) => {
            if (info.file.status === 'done') {
              message.success(`${info.file.name} 上传成功`);
              fetchDocuments(selectedDataset!.id);
              setDocumentModalVisible(false);
            } else if (info.file.status === 'error') {
              message.error(`${info.file.name} 上传失败`);
            }
          }}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 TXT、MARKDOWN、MDX、PDF、HTML、XLSX、XLS、DOCX、CSV、VTT、PROPERTIES、MD、HTM 格式文件，每个文件不超过 15MB
          </p>
        </Upload.Dragger>
      </Modal>
    </Card>
  );
};

export default KnowledgeBaseManager; 