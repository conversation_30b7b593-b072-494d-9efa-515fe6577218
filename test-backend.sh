#!/bin/bash

echo "=== 测试后端服务器状态 ==="

# 测试后端API是否正常
echo "1. 测试配置API："
curl -s -o /dev/null -w "%{http_code}" "https://jiaohutest.jingyuncenter.com/api/config"
echo ""

echo "2. 测试知识库API："
curl -s -o /dev/null -w "%{http_code}" "https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets"
echo ""

echo "3. 获取配置详情："
curl -s "https://jiaohutest.jingyuncenter.com/api/config" | head -c 200
echo ""

echo "4. 获取知识库列表详情："
curl -s "https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets" | head -c 200
echo ""

echo "=== 测试完成 ==="
