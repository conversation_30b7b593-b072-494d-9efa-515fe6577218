# 用于调试的Nginx配置
server {
    listen 443 ssl;
    server_name jiaohu.jingyuncenter.com;

    # SSL 配置（根据实际情况修改）
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # 根目录配置
    root /www/wwwroot/szr.jingyuncenter.com;
    index index.html;
    
    # 增加调试日志
    access_log /var/log/nginx/jiaohu.access.log;
    error_log /var/log/nginx/jiaohu.error.log debug;

    # 代理 API 请求到后端服务器 - 添加更多调试信息
    location /api/ {
        # 添加调试头信息
        add_header X-Debug-Backend "Proxying to localhost:3001" always;
        
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 增加超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 错误处理
        proxy_intercept_errors on;
        error_page 502 503 504 /api_error.html;
    }
    
    # API错误页面
    location = /api_error.html {
        internal;
        return 502 '{"error": "Backend server is not available"}';
        add_header Content-Type application/json;
    }
    
    # 处理静态资源
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, max-age=31536000";
        try_files $uri =404;
    }
    
    # 处理上传文件的请求
    location /uploads/ {
        alias /www/wwwroot/szr.jingyuncenter.com/uploads/;
        try_files $uri =404;
    }
    
    # 处理所有其他请求，重定向到 index.html 以支持 React Router
    location / {
        try_files $uri $uri/ /index.html;
    }
}
