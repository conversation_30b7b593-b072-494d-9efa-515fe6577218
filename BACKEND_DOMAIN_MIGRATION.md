# 后端域名统一配置说明

## 修改概述

已将前端项目配置为统一使用后端域名 `https://jiaohutest.jingyuncenter.com`，不再依赖 nginx 代理。

## 修改内容

### 1. 前端配置文件修改

#### `src/config/constants.ts`
- 保持原有配置不变，生产环境使用 `https://jiaohutest.jingyuncenter.com`
- 添加注释说明统一使用后端域名

#### `src/components/KnowledgeBaseManager.tsx`
- 导入 `API_BASE_URL` 常量
- 将所有相对路径 `/api/...` 改为使用 `${API_BASE_URL}/...`
- 修改的接口包括：
  - 获取知识库列表
  - 创建知识库
  - 获取文档列表
  - 创建文档（文本）
  - 删除文档
  - 文件上传

#### `vite.config.ts`
- 移除代理配置
- 添加 `host: true` 允许外部访问

### 2. 其他服务已正确配置

以下服务已经正确使用 `API_BASE_URL`：
- `src/services/configService.ts` - 配置服务
- `src/services/uploadService.ts` - 上传服务
- `src/services/knowledgeBaseService.ts` - 知识库服务
- `src/components/DigitalHuman.tsx` - 数字人组件

## 优势

1. **统一的API地址**：所有请求都直接指向后端域名
2. **简化部署**：不需要配置 nginx 代理
3. **跨域支持**：后端已配置 CORS 支持跨域请求
4. **开发一致性**：开发和生产环境使用相同的请求方式

## 注意事项

1. **CORS配置**：确保后端服务器已正确配置 CORS
2. **SSL证书**：确保后端域名有有效的SSL证书
3. **防火墙**：确保后端服务器端口（3001）可以被外部访问

## 测试建议

1. 在开发环境测试所有功能正常
2. 构建生产版本并测试
3. 确认知识库管理功能正常工作
4. 验证文件上传功能正常

## 回滚方案

如果需要回滚到 nginx 代理方式：

1. 恢复 `vite.config.ts` 中的代理配置
2. 将 `KnowledgeBaseManager.tsx` 中的 `${API_BASE_URL}` 改回相对路径 `/api`
3. 配置 nginx 代理规则

## 相关文件

- `src/config/constants.ts` - API地址配置
- `src/components/KnowledgeBaseManager.tsx` - 知识库管理组件
- `vite.config.ts` - Vite配置
- `server/index.ts` - 后端服务器（已配置CORS）
