# API 请求问题排查指南

## 问题描述
访问 `/api/knowledge-base/datasets` 时返回空白页（index.html），而不是 API 响应。

## 可能原因分析

### 1. 浏览器缓存问题
- 浏览器可能缓存了旧版本的 JavaScript 文件
- 需要强制刷新或清除缓存

### 2. 构建版本问题
- 当前运行的可能是旧版本的构建文件
- 需要重新构建项目

### 3. 环境变量问题
- 生产环境可能没有正确识别 `import.meta.env.DEV`
- 需要确认环境变量设置

## 排查步骤

### 步骤 1：检查当前环境
```bash
# 检查当前是否为开发环境
echo $NODE_ENV

# 检查构建模式
npm run build
```

### 步骤 2：清除缓存并重新构建
```bash
# 清除 node_modules 和构建缓存
rm -rf node_modules dist
npm install
npm run build
```

### 步骤 3：检查构建后的文件
查看 `dist/assets/index-*.js` 文件中是否包含正确的 API 地址：
- 应该包含 `https://jiaohutest.jingyuncenter.com/api`
- 不应该包含相对路径 `/api/`

### 步骤 4：浏览器调试
1. 打开浏览器开发者工具
2. 查看 Network 标签页
3. 确认请求的实际 URL
4. 检查是否有 CORS 错误

### 步骤 5：临时测试方案
可以临时在 `KnowledgeBaseManager.tsx` 中硬编码 API 地址进行测试：

```typescript
// 临时测试用，确认问题
const response = await axios.get('https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets');
```

## 预期结果

### 正确的请求流程
1. 前端发起请求：`https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets`
2. 后端接收请求并处理
3. 返回 JSON 数据而不是 HTML

### 错误的请求流程
1. 前端发起请求：`https://jiaohu.jingyuncenter.com/api/knowledge-base/datasets`
2. Nginx 找不到对应文件
3. 返回 `index.html`（因为 `try_files` 配置）

## 解决方案

### 方案 1：确认构建正确
重新构建并部署最新版本

### 方案 2：临时添加 Nginx 代理（如果需要）
如果问题持续，可以临时在 `jiaohu.jingyuncenter.com` 的 Nginx 配置中添加：

```nginx
location /api/ {
    proxy_pass https://jiaohutest.jingyuncenter.com;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

### 方案 3：检查 CORS 配置
确认后端服务器的 CORS 配置允许来自 `jiaohu.jingyuncenter.com` 的请求。

## 验证方法

### 1. 直接测试 API
```bash
curl -X GET "https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets" \
  -H "Content-Type: application/json"
```

### 2. 检查前端请求
在浏览器控制台中执行：
```javascript
fetch('https://jiaohutest.jingyuncenter.com/api/knowledge-base/datasets')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));
```

## 注意事项

1. **CORS 策略**：确保后端允许跨域请求
2. **SSL 证书**：确保后端域名有有效的 SSL 证书
3. **防火墙**：确保后端服务器端口可以被外部访问
4. **DNS 解析**：确认域名解析正确
