#!/bin/bash

echo "=== 启动后端服务器 ==="

# 进入服务器目录
cd server

# 检查是否存在编译后的文件
if [ ! -f "dist/index.js" ]; then
    echo "编译后的文件不存在，正在编译..."
    npm run build
fi

# 检查编译是否成功
if [ ! -f "dist/index.js" ]; then
    echo "编译失败，请检查TypeScript代码"
    exit 1
fi

echo "启动后端服务器..."

# 使用PM2启动（推荐生产环境）
if command -v pm2 &> /dev/null; then
    echo "使用PM2启动服务器..."
    pm2 start dist/index.js --name "digital-human-backend" --watch
    pm2 save
    pm2 startup
else
    echo "PM2未安装，使用node直接启动..."
    echo "建议安装PM2: npm install -g pm2"
    node dist/index.js
fi
